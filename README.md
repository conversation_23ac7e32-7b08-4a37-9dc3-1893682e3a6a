# Atlas

A sophisticated file monitoring and task processing system that watches directories for new files and automatically processes them based on their type and content. The system features an advanced **Home Media Intelligence System** with AI-driven tagging, transcription, and semantic search capabilities. It supports various types of tasks including shell commands, media processing, AI analysis, and custom tool executions.

<p align="center">
  <img src="./assets/atlas-banner.png" alt="Atlas carrying a data planet" width="600"/>
</p>

## System Architecture

The system is built with a modular, event-driven architecture that combines local processing with optional cloud AI capabilities:

### Core Components

-   **File System Watcher**: Monitors designated directories for new files
-   **Task Orchestrator**: Manages task execution and dependencies
-   **Task Execution Engine**: Handles different types of tasks:
    -   Shell command execution
    -   LLM-based processing (local Ollama or cloud OpenAI)
    -   Media processing and metadata extraction
    -   Custom tool execution
    -   YouTube downloads
    -   Code generation and execution
    -   Task planning and review
-   **Database Layer**: SQLite-based storage with migration support
-   **MCP Integration Layer**: Manages AI-powered learning and monitoring
-   **Retry & Scheduling System**: Handles task retries and periodic jobs

### External Services Integration

-   **Ollama**: Local LLM processing (default: qwen3:8b)
-   **OpenAI**: Cloud-based AI processing (optional)
-   **ChromaDB**: Vector storage for semantic search and embeddings
-   **MeiliSearch**: Fast full-text search engine with filtering capabilities
-   **Whisper**: AI-powered speech recognition for audio/video transcription
-   **File System**: Local storage with organized directory structure

## Features

-   **File Monitoring**: Automatically detects and processes new files in configured directories
-   **Task Processing**: Supports multiple types of tasks:
    -   Shell command execution
    -   Media file processing and metadata extraction
    -   Custom tool execution
    -   Task dependencies and parent-child relationships
-   **Home Media Intelligence System** (✅ **IMPLEMENTED**):
    -   **AI-Powered Transcription**: Automatic speech-to-text using Whisper for videos and audio
    -   **Smart Tagging**: LLM-generated tags with human-readable keywords and explanations
    -   **Semantic Search**: ChromaDB-powered similarity search for content discovery
    -   **Full-Text Search**: MeiliSearch integration for fast keyword and filtered searches
    -   **Privacy-First**: All AI processing happens locally with no external API calls
    -   **CLI Tools**: Comprehensive command-line interface for search and tag management
    -   **Content Analysis**: Multi-modal processing (audio, video frames, text) for comprehensive tagging
    -   **Privacy-First**: All AI processing happens locally on your device
-   **Media Processing**:
    -   Automatic detection of media files (video and audio)
    -   Metadata extraction using ffprobe/mediainfo
    -   Frame extraction and visual analysis
    -   Audio embedding and classification
    -   Deduplication support
    -   Organized media collection management
-   **Task Management**:
    -   Task scheduling and orchestration
    -   Retry mechanism for failed tasks
    -   Dependency tracking and management
    -   Task status monitoring and error handling
-   **Storage Integration**:
    -   S3 bucket support for file storage
    -   Local file system organization
    -   Automatic file archiving and error handling
-   **AI Integration**:
    -   OpenAI GPT-4 support
    -   Ollama integration for local AI processing
    -   ChromaDB for vector storage and similarity search
    -   Whisper for automatic speech recognition and transcription
    -   MeiliSearch for fast full-text search with tag filtering
    -   CLIP/OpenCLIP for image and video frame analysis
    -   Multi-modal content understanding and tagging
-   **Learning & Optimization**:
    -   Task pattern analysis
    -   Performance monitoring
    -   Smart recommendations
    -   Automated task planning

## Directory Structure

The system uses the following directory structure:

-   `incoming/`: New files to be processed
-   `processing/`: Files currently being processed
-   `archive/`: Successfully processed files
-   `error/`: Files that encountered errors during processing
-   `outputs/`: Generated output files
-   `logs/`: System logs
-   `dashboard/`: Web dashboard files
-   `media/`: Processed media files
-   `tasks/`: Task definition files

## Prerequisites

-   [Bun](https://bun.sh) v1.2.14 or later
-   [Ollama](https://ollama.ai) for local AI processing
-   [ChromaDB](https://www.trychroma.com/) for vector storage
-   [MeiliSearch](https://www.meilisearch.com/) for full-text search
-   [Python 3.10+](https://python.org) with pip for Whisper
-   [OpenAI Whisper](https://github.com/openai/whisper) for speech recognition
-   FFmpeg/ffprobe for media processing
-   MediaInfo (optional, for alternative media metadata extraction)

## Installation

1. Clone the repository
2. Install Node.js dependencies:

```bash
bun install
```

3. Install Python dependencies for Whisper:

```bash
pip install openai-whisper
```

4. Download MeiliSearch:
   - Download the latest release from [MeiliSearch releases](https://github.com/meilisearch/meilisearch/releases/latest)
   - Place the executable in your project directory or system PATH

## Configuration

The system uses environment variables for configuration. Key configurations include:

### AI Services
-   `OPENAI_API_KEY`: Your OpenAI API key
-   `OLLAMA_URL`: Ollama server URL (default: http://localhost:11434)
-   `OLLAMA_MODEL`: Model to use with Ollama (default: qwen3:8b)

### Search & Storage
-   `CHROMA_URL`: ChromaDB server URL (default: http://localhost:8000)
-   `MEILISEARCH_URL`: MeiliSearch server URL (default: http://localhost:7700)
-   `MEILISEARCH_MASTER_KEY`: MeiliSearch master key (optional for development)

### Media Processing
-   `WHISPER_MODEL`: Whisper model to use (default: turbo)
-   `WHISPER_DEVICE`: Device for Whisper processing (default: cpu)

### AWS S3 Integration
-   `AWS_ACCESS_KEY_ID`
-   `AWS_SECRET_ACCESS_KEY`
-   `AWS_DEFAULT_REGION`
-   `S3_ENDPOINT`
-   `S3_DEFAULT_BUCKET`

## Running the System

### Start External Services

1. **Start the Ollama server:**

```bash
ollama serve
```

2. **Start the ChromaDB server:**

```bash
chroma run --path ./chroma_db
```

3. **Start the MeiliSearch server:**

```bash
# Basic startup (development mode)
./meilisearch.exe --db-path ./meilisearch_db --http-addr 127.0.0.1:7700

# Production mode with master key
./meilisearch.exe --db-path ./meilisearch_db --http-addr 127.0.0.1:7700 --master-key your-secure-master-key
```

4. **Verify Whisper installation:**

```bash
whisper --help
```

### Start the Main Application

5. **Start the folder watcher:**

```bash
bun run dev
```

### Service Health Checks

You can verify that all services are running:

```bash
# Check MeiliSearch
curl http://127.0.0.1:7700/health

# Check ChromaDB
curl http://localhost:8000/api/v1/heartbeat

# Check Ollama
curl http://localhost:11434/api/tags

# Test Whisper (with any audio file)
whisper path/to/audio.mp3 --model turbo --output_format json
```

## Home Media Intelligence System

The system now includes a comprehensive AI-driven media processing pipeline:

### Media Processing Pipeline

1. **File Detection**: Monitors media folders and creates ingestion tasks
2. **Transcription**: Uses Whisper to convert audio/video to text
3. **Content Analysis**:
   - Extracts key frames from videos
   - Analyzes visual content with CLIP/OpenCLIP
   - Processes audio embeddings
4. **AI Tagging**: LLM generates descriptive tags with explanations
5. **Indexing**:
   - MeiliSearch for keyword and filtered search
   - ChromaDB for semantic similarity search

### CLI Commands

The system provides comprehensive CLI commands for media management:

```bash
# Search for content
npm run media-search "funny cat videos"
npm run media-search --filter "tags:comedy AND year:2020"
npm run media-search "baby crawling" --semantic
npm run media-search --similar /path/to/video.mp4

# Manage tags
npm run media-tags list /path/to/video.mp4
npm run media-tags add /path/to/video.mp4 "comedy" "family"
npm run media-tags remove /path/to/video.mp4 "horror"
npm run media-tags retag /path/to/video.mp4 --explain
npm run media-tags explain /path/to/video.mp4

# Media processing
npm run media-ingest /path/to/video.mp4
npm run media-organize /path/to/video.mp4
```

## Implementation Status

### ✅ Phase 1: Home Media Intelligence System (COMPLETED)

-   ✅ Whisper transcription integration with chunked timestamps
-   ✅ MeiliSearch full-text search with filtering
-   ✅ ChromaDB semantic similarity search
-   ✅ Advanced AI tagging pipeline with LLM explanations
-   ✅ Complete CLI interface for search and tag management
-   ✅ Privacy-first local processing architecture
-   ✅ Automated task orchestration pipeline

### Phase 2: Advanced AI Features (1-3 months)

-   Content summarization
-   Automated content recommendations
-   Advanced visual analysis (scene detection, object recognition)
-   Audio classification and music analysis

### Phase 3: User Experience & Integration (3-6 months)

-   Web dashboard for media browsing
-   Tag editing and curation interface
-   Batch processing and management
-   External service integrations

### Phase 4: Learning & Optimization (6-9 months)

-   Pattern analysis and learning
-   Performance optimization
-   Autonomous task planning
-   User feedback integration

## Development

The system is built with TypeScript and uses Bun as its runtime. The codebase follows a modular architecture with clear separation of concerns:

-   `src/index.ts`: Main application entry point
-   `src/db.ts`: Database layer and migrations
-   `src/executors/`: Task execution implementations
-   `src/mcp/`: AI integration and learning system
-   `src/utils/`: Helper functions and utilities
-   `src/types/`: TypeScript type definitions

## License

This project was created using `bun init` in bun v1.2.14. [Bun](https://bun.sh) is a fast all-in-one JavaScript runtime.
